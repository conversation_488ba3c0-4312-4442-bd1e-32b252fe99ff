import java.util.List;

public class App {
    public static void main(String[] args) throws Exception {
        Extractor extractor = new Extractor();
        List<String> data;
        try{
            data = extractor.readDataFromFile("lib/data.txt");
        }catch(Error e){
            System.out.println("File not found");
            throw e;
        }

        System.out.println(data);

    }
}
